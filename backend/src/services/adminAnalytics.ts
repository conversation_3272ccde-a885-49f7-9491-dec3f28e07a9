import { query } from '../database/config.js';

export interface AdminAnalyticsData {
  overview: {
    totalUsers: number;
    activeSessions: number;
    totalMessages: number;
    emotionCaptureRate: number;
  };
  emotions: {
    topEmotions: Array<{ emotion: string; avgScore: number; count: number }>;
    emotionTrends: Array<{ date: string; [emotion: string]: number | string }>;
  };
  engagement: {
    avgSessionDuration: number;
    avgMessagesPerSession: number;
    returnUserRate: number;
  };
  system: {
    voiceProcessingHealth: number;
    storageUsage: { totalFiles: number; totalSize: string };
    errorRate: number;
    activeConnections: number;
  };
}

export class AdminAnalyticsService {
  /**
   * Get comprehensive admin analytics
   */
  async getAdminAnalytics(days: number = 30): Promise<AdminAnalyticsData> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const [overview, emotions, engagement, system] = await Promise.all([
      this.getOverviewMetrics(cutoffDate),
      this.getEmotionAnalytics(cutoffDate),
      this.getEngagementMetrics(cutoffDate),
      this.getSystemMetrics()
    ]);

    return {
      overview,
      emotions,
      engagement,
      system
    };
  }

  /**
   * Get overview metrics
   */
  private async getOverviewMetrics(cutoffDate: Date) {
    // Total users
    const totalUsersResult = await query('SELECT COUNT(*) as count FROM users');
    const totalUsers = parseInt(totalUsersResult.rows[0].count);

    // Active sessions (sessions started in the last 24 hours)
    const activeSessionsResult = await query(`
      SELECT COUNT(*) as count 
      FROM chat_sessions 
      WHERE started_at >= NOW() - INTERVAL '24 hours' 
      AND status = 'active'
    `);
    const activeSessions = parseInt(activeSessionsResult.rows[0].count);

    // Total messages in time period
    const totalMessagesResult = await query(`
      SELECT COUNT(*) as count 
      FROM conversation_messages cm
      JOIN chat_sessions cs ON cm.session_id = cs.id
      WHERE cs.started_at >= $1
    `, [cutoffDate]);
    const totalMessages = parseInt(totalMessagesResult.rows[0].count);

    // Emotion capture rate
    const emotionCaptureResult = await query(`
      SELECT 
        COUNT(*) as total_messages,
        COUNT(CASE WHEN emotions != '{}' THEN 1 END) as messages_with_emotions
      FROM conversation_messages cm
      JOIN chat_sessions cs ON cm.session_id = cs.id
      WHERE cs.started_at >= $1
    `, [cutoffDate]);
    
    const emotionData = emotionCaptureResult.rows[0];
    const emotionCaptureRate = emotionData.total_messages > 0 
      ? (emotionData.messages_with_emotions / emotionData.total_messages) * 100 
      : 0;

    return {
      totalUsers,
      activeSessions,
      totalMessages,
      emotionCaptureRate: Math.round(emotionCaptureRate * 100) / 100
    };
  }

  /**
   * Get emotion analytics
   */
  private async getEmotionAnalytics(cutoffDate: Date) {
    // Top emotions
    const topEmotionsResult = await query(`
      WITH emotion_data AS (
        SELECT 
          jsonb_each_text(emotions) as emotion_pair
        FROM conversation_messages cm
        JOIN chat_sessions cs ON cm.session_id = cs.id
        WHERE cs.started_at >= $1 AND emotions != '{}'
      ),
      emotion_scores AS (
        SELECT 
          (emotion_pair).key as emotion,
          (emotion_pair).value::float as score
        FROM emotion_data
      )
      SELECT 
        emotion,
        COUNT(*) as occurrence_count,
        ROUND(AVG(score)::numeric, 4) as avg_score
      FROM emotion_scores
      WHERE score > 0.1
      GROUP BY emotion
      ORDER BY avg_score DESC
      LIMIT 10
    `, [cutoffDate]);

    const topEmotions = topEmotionsResult.rows.map((row: any) => ({
      emotion: row.emotion,
      avgScore: parseFloat(row.avg_score),
      count: parseInt(row.occurrence_count)
    }));

    // Emotion trends (daily averages for the last 7 days)
    const emotionTrendsResult = await query(`
      SELECT 
        DATE(cm.timestamp) as date,
        ROUND(AVG((cm.emotions->>'joy')::float)::numeric, 3) as joy,
        ROUND(AVG((cm.emotions->>'excitement')::float)::numeric, 3) as excitement,
        ROUND(AVG((cm.emotions->>'calmness')::float)::numeric, 3) as calmness,
        ROUND(AVG((cm.emotions->>'anxiety')::float)::numeric, 3) as anxiety,
        ROUND(AVG((cm.emotions->>'satisfaction')::float)::numeric, 3) as satisfaction
      FROM conversation_messages cm
      JOIN chat_sessions cs ON cm.session_id = cs.id
      WHERE cs.started_at >= NOW() - INTERVAL '7 days' 
      AND cm.emotions != '{}'
      GROUP BY DATE(cm.timestamp)
      ORDER BY date DESC
      LIMIT 7
    `);

    const emotionTrends = emotionTrendsResult.rows.map((row: any) => ({
      date: row.date.toISOString().split('T')[0],
      joy: parseFloat(row.joy) || 0,
      excitement: parseFloat(row.excitement) || 0,
      calmness: parseFloat(row.calmness) || 0,
      anxiety: parseFloat(row.anxiety) || 0,
      satisfaction: parseFloat(row.satisfaction) || 0
    }));

    return {
      topEmotions,
      emotionTrends
    };
  }

  /**
   * Get engagement metrics
   */
  private async getEngagementMetrics(cutoffDate: Date) {
    // Session duration
    const sessionStatsResult = await query(`
      SELECT
        AVG(EXTRACT(EPOCH FROM (ended_at - started_at))/60) as avg_duration
      FROM chat_sessions
      WHERE started_at >= $1
    `, [cutoffDate]);

    const sessionStats = sessionStatsResult.rows[0];

    // Average messages per session
    const messagesPerSessionResult = await query(`
      SELECT AVG(message_count) as avg_messages
      FROM (
        SELECT COUNT(cm.id) as message_count
        FROM chat_sessions cs
        LEFT JOIN conversation_messages cm ON cs.id = cm.session_id
        WHERE cs.started_at >= $1
        GROUP BY cs.id
      ) session_messages
    `, [cutoffDate]);

    const avgMessagesPerSession = parseFloat(messagesPerSessionResult.rows[0].avg_messages) || 0;

    // Return user rate (users with more than one session)
    const returnUserResult = await query(`
      WITH user_session_counts AS (
        SELECT 
          user_id,
          COUNT(*) as session_count
        FROM chat_sessions 
        WHERE started_at >= $1
        GROUP BY user_id
      )
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN session_count > 1 THEN 1 END) as return_users
      FROM user_session_counts
    `, [cutoffDate]);

    const returnUserData = returnUserResult.rows[0];
    const returnUserRate = returnUserData.total_users > 0 
      ? (returnUserData.return_users / returnUserData.total_users) * 100 
      : 0;

    return {
      avgSessionDuration: Math.round((parseFloat(sessionStats.avg_duration) || 0) * 100) / 100,
      avgMessagesPerSession: Math.round(avgMessagesPerSession * 100) / 100,
      returnUserRate: Math.round(returnUserRate * 100) / 100
    };
  }

  /**
   * Get system metrics
   */
  private async getSystemMetrics() {
    // Voice processing health (success rate)
    const voiceHealthResult = await query(`
      SELECT 
        COUNT(*) as total_uploads,
        COUNT(CASE WHEN upload_status = 'completed' THEN 1 END) as successful_uploads
      FROM audio_data 
      WHERE created_at >= NOW() - INTERVAL '24 hours'
    `);

    const voiceHealth = voiceHealthResult.rows[0];
    const voiceProcessingHealth = voiceHealth.total_uploads > 0 
      ? (voiceHealth.successful_uploads / voiceHealth.total_uploads) * 100 
      : 100;

    // Storage usage
    const storageResult = await query(`
      SELECT 
        COUNT(*) as total_files,
        SUM(file_size) as total_size
      FROM audio_data
    `);

    const storage = storageResult.rows[0];
    const totalSizeBytes = parseInt(storage.total_size) || 0;
    const totalSizeGB = totalSizeBytes / (1024 * 1024 * 1024);
    const totalSizeFormatted = totalSizeGB > 1 
      ? `${totalSizeGB.toFixed(2)} GB` 
      : `${(totalSizeBytes / (1024 * 1024)).toFixed(2)} MB`;

    // Error rate (failed sessions in last 24 hours)
    const errorRateResult = await query(`
      SELECT 
        COUNT(*) as total_sessions,
        COUNT(CASE WHEN status = 'error' THEN 1 END) as error_sessions
      FROM chat_sessions 
      WHERE started_at >= NOW() - INTERVAL '24 hours'
    `);

    const errorData = errorRateResult.rows[0];
    const errorRate = errorData.total_sessions > 0 
      ? (errorData.error_sessions / errorData.total_sessions) * 100 
      : 0;

    // Active connections (mock data - would come from WebSocket server)
    const activeConnections = 12; // This would be fetched from WebSocket server stats

    return {
      voiceProcessingHealth: Math.round(voiceProcessingHealth * 100) / 100,
      storageUsage: {
        totalFiles: parseInt(storage.total_files) || 0,
        totalSize: totalSizeFormatted
      },
      errorRate: Math.round(errorRate * 100) / 100,
      activeConnections
    };
  }
}

export const adminAnalyticsService = new AdminAnalyticsService();
