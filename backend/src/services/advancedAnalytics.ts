import { query } from '../database/config.js';

export interface EmotionCorrelation {
  emotion1: string;
  emotion2: string;
  correlation: number;
  cooccurrenceCount: number;
  significance: number;
}

export interface UserCohort {
  cohortId: string;
  cohortName: string;
  userCount: number;
  retentionRate: number;
  avgSessionDuration: number;
  avgEmotionScore: number;
  characteristics: Record<string, any>;
}

export interface EmotionAnomaly {
  userId: string;
  sessionId: string;
  timestamp: Date;
  emotion: string;
  score: number;
  expectedScore: number;
  deviationScore: number;
  severity: 'low' | 'medium' | 'high';
}

export interface PredictiveInsight {
  type: 'churn_risk' | 'engagement_opportunity' | 'emotional_concern';
  userId: string;
  probability: number;
  factors: Array<{ factor: string; weight: number }>;
  recommendedAction: string;
  confidence: number;
}

export class AdvancedAnalyticsService {
  /**
   * Analyze emotion correlations across all conversations
   */
  async getEmotionCorrelations(days = 30): Promise<EmotionCorrelation[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const result = await query(`
      WITH emotion_pairs AS (
        SELECT 
          e1.emotion as emotion1,
          e2.emotion as emotion2,
          e1.score as score1,
          e2.score as score2,
          e1.session_id
        FROM emotion_analytics e1
        JOIN emotion_analytics e2 ON e1.session_id = e2.session_id 
          AND e1.timestamp = e2.timestamp 
          AND e1.emotion < e2.emotion
        WHERE e1.timestamp >= $1 
          AND e1.score > 0.1 
          AND e2.score > 0.1
      ),
      correlation_stats AS (
        SELECT 
          emotion1,
          emotion2,
          COUNT(*) as cooccurrence_count,
          CORR(score1, score2) as correlation,
          STDDEV(score1) * STDDEV(score2) as variance_product
        FROM emotion_pairs
        GROUP BY emotion1, emotion2
        HAVING COUNT(*) >= 10
      )
      SELECT 
        emotion1,
        emotion2,
        COALESCE(correlation, 0) as correlation,
        cooccurrence_count,
        CASE 
          WHEN cooccurrence_count >= 50 THEN 'high'
          WHEN cooccurrence_count >= 20 THEN 'medium'
          ELSE 'low'
        END as significance
      FROM correlation_stats
      WHERE ABS(COALESCE(correlation, 0)) > 0.3
      ORDER BY ABS(correlation) DESC
      LIMIT 50
    `, [cutoffDate]);

    return result.rows.map((row: any) => ({
      emotion1: row.emotion1,
      emotion2: row.emotion2,
      correlation: parseFloat(row.correlation) || 0,
      cooccurrenceCount: parseInt(row.cooccurrence_count),
      significance: parseFloat(row.cooccurrence_count) >= 50 ? 0.9 : 
                   parseFloat(row.cooccurrence_count) >= 20 ? 0.7 : 0.5
    }));
  }

  /**
   * Perform user cohort analysis
   */
  async getUserCohorts(): Promise<UserCohort[]> {
    const result = await query(`
      WITH user_stats AS (
        SELECT 
          u.id as user_id,
          u.created_at,
          DATE_TRUNC('month', u.created_at) as signup_month,
          COUNT(DISTINCT cs.id) as session_count,
          AVG(EXTRACT(EPOCH FROM (cs.ended_at - cs.started_at))/60) as avg_session_duration,
          AVG(
            CASE WHEN cm.emotions != '{}' THEN
              (SELECT AVG(value::float) FROM jsonb_each_text(cm.emotions))
            END
          ) as avg_emotion_score,
          MAX(cs.started_at) as last_session_at
        FROM users u
        LEFT JOIN chat_sessions cs ON u.id = cs.user_id
        LEFT JOIN conversation_messages cm ON cs.id = cm.session_id
        WHERE u.created_at >= NOW() - INTERVAL '12 months'
        GROUP BY u.id, u.created_at
      ),
      cohort_analysis AS (
        SELECT 
          signup_month,
          COUNT(*) as user_count,
          AVG(session_count) as avg_sessions,
          AVG(avg_session_duration) as avg_duration,
          AVG(avg_emotion_score) as avg_emotion,
          COUNT(CASE WHEN last_session_at >= NOW() - INTERVAL '30 days' THEN 1 END)::float / COUNT(*) as retention_rate
        FROM user_stats
        GROUP BY signup_month
        HAVING COUNT(*) >= 1
      )
      SELECT
        TO_CHAR(signup_month, 'YYYY-MM') as cohort_id,
        TO_CHAR(signup_month, 'Month YYYY') as cohort_name,
        user_count,
        ROUND((retention_rate * 100)::numeric, 2) as retention_rate,
        ROUND(avg_duration::numeric, 2) as avg_session_duration,
        ROUND(avg_emotion::numeric, 3) as avg_emotion_score
      FROM cohort_analysis
      ORDER BY signup_month DESC
    `);

    return result.rows.map((row: any) => ({
      cohortId: row.cohort_id,
      cohortName: row.cohort_name,
      userCount: parseInt(row.user_count),
      retentionRate: parseFloat(row.retention_rate),
      avgSessionDuration: parseFloat(row.avg_session_duration) || 0,
      avgEmotionScore: parseFloat(row.avg_emotion_score) || 0,
      characteristics: {
        signupPeriod: row.cohort_name,
        isRecent: new Date(row.cohort_id + '-01') > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
      }
    }));
  }

  /**
   * Detect emotional anomalies
   */
  async detectEmotionAnomalies(days = 7): Promise<EmotionAnomaly[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const result = await query(`
      WITH user_emotion_baselines AS (
        SELECT 
          user_id,
          emotion,
          AVG(score) as baseline_score,
          STDDEV(score) as score_stddev
        FROM emotion_analytics
        WHERE timestamp >= NOW() - INTERVAL '30 days'
          AND timestamp < $1
          AND score > 0
        GROUP BY user_id, emotion
        HAVING COUNT(*) >= 5
      ),
      recent_emotions AS (
        SELECT 
          ea.user_id,
          ea.session_id,
          ea.timestamp,
          ea.emotion,
          ea.score,
          ueb.baseline_score,
          ueb.score_stddev
        FROM emotion_analytics ea
        JOIN user_emotion_baselines ueb ON ea.user_id = ueb.user_id AND ea.emotion = ueb.emotion
        WHERE ea.timestamp >= $1
          AND ea.score > 0
      ),
      anomalies AS (
        SELECT 
          user_id,
          session_id,
          timestamp,
          emotion,
          score,
          baseline_score,
          ABS(score - baseline_score) / NULLIF(score_stddev, 0) as deviation_score
        FROM recent_emotions
        WHERE score_stddev > 0
          AND ABS(score - baseline_score) / score_stddev > 2
      )
      SELECT 
        user_id,
        session_id,
        timestamp,
        emotion,
        score,
        baseline_score,
        deviation_score,
        CASE 
          WHEN deviation_score > 4 THEN 'high'
          WHEN deviation_score > 3 THEN 'medium'
          ELSE 'low'
        END as severity
      FROM anomalies
      ORDER BY deviation_score DESC
      LIMIT 100
    `, [cutoffDate]);

    return result.rows.map((row: any) => ({
      userId: row.user_id,
      sessionId: row.session_id,
      timestamp: row.timestamp,
      emotion: row.emotion,
      score: parseFloat(row.score),
      expectedScore: parseFloat(row.baseline_score),
      deviationScore: parseFloat(row.deviation_score),
      severity: row.severity as 'low' | 'medium' | 'high'
    }));
  }

  /**
   * Generate predictive insights
   */
  async getPredictiveInsights(): Promise<PredictiveInsight[]> {
    const insights: PredictiveInsight[] = [];

    // Churn risk prediction
    const churnRiskResult = await query(`
      WITH user_activity AS (
        SELECT 
          u.id as user_id,
          COUNT(cs.id) as total_sessions,
          MAX(cs.started_at) as last_session,
          AVG(EXTRACT(EPOCH FROM (cs.ended_at - cs.started_at))/60) as avg_duration,
          COUNT(CASE WHEN cs.started_at >= NOW() - INTERVAL '7 days' THEN 1 END) as recent_sessions,
          AVG(
            CASE WHEN cm.emotions != '{}' THEN
              (cm.emotions->>'anxiety')::float + (cm.emotions->>'sadness')::float
            END
          ) as negative_emotion_avg
        FROM users u
        LEFT JOIN chat_sessions cs ON u.id = cs.user_id
        LEFT JOIN conversation_messages cm ON cs.id = cm.session_id
        WHERE u.created_at <= NOW() - INTERVAL '14 days'
        GROUP BY u.id
      )
      SELECT 
        user_id,
        CASE 
          WHEN recent_sessions = 0 AND last_session < NOW() - INTERVAL '14 days' THEN 0.9
          WHEN recent_sessions <= 1 AND avg_duration < 3 THEN 0.7
          WHEN negative_emotion_avg > 0.6 THEN 0.6
          ELSE 0.2
        END as churn_probability,
        total_sessions,
        recent_sessions,
        avg_duration,
        negative_emotion_avg
      FROM user_activity
      WHERE (recent_sessions = 0 AND last_session < NOW() - INTERVAL '7 days')
         OR (recent_sessions <= 1 AND avg_duration < 5)
         OR negative_emotion_avg > 0.5
      ORDER BY churn_probability DESC
      LIMIT 50
    `);

    churnRiskResult.rows.forEach((row: any) => {
      const factors = [];
      if (row.recent_sessions === 0) factors.push({ factor: 'No recent activity', weight: 0.4 });
      if (row.avg_duration < 3) factors.push({ factor: 'Short session duration', weight: 0.3 });
      if (row.negative_emotion_avg > 0.5) factors.push({ factor: 'High negative emotions', weight: 0.3 });

      insights.push({
        type: 'churn_risk',
        userId: row.user_id,
        probability: parseFloat(row.churn_probability),
        factors,
        recommendedAction: row.churn_probability > 0.7 ? 
          'Immediate re-engagement campaign' : 
          'Monitor and provide personalized content',
        confidence: 0.8
      });
    });

    return insights;
  }

  /**
   * Get real-time emotion flow analysis
   */
  async getEmotionFlowAnalysis(hours = 24): Promise<{
    currentTrends: Array<{ emotion: string; trend: 'rising' | 'falling' | 'stable'; change: number }>;
    peakEmotions: Array<{ emotion: string; peakTime: Date; intensity: number }>;
    emotionTransitions: Array<{ from: string; to: string; frequency: number; avgTimeDiff: number }>;
  }> {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);

    try {
      // Simplified current trends - just get recent emotion averages
      const trendsResult = await query(`
        SELECT
          emotion,
          AVG(score) as avg_score,
          COUNT(*) as data_points,
          MAX(score) - MIN(score) as score_range
        FROM emotion_analytics
        WHERE timestamp >= $1
          AND score > 0.1
        GROUP BY emotion
        HAVING COUNT(*) >= 5
        ORDER BY avg_score DESC
        LIMIT 20
      `, [cutoffTime]);

      const currentTrends = trendsResult.rows.map((row: any) => ({
        emotion: row.emotion,
        trend: row.score_range > 0.3 ? 'rising' :
               row.score_range < 0.1 ? 'stable' : 'falling',
        change: parseFloat(row.avg_score)
      }));

      // Simplified peak emotions - just get highest scoring emotions
      const peaksResult = await query(`
        SELECT
          emotion,
          timestamp as peak_time,
          score as intensity
        FROM emotion_analytics
        WHERE timestamp >= $1
          AND score > 0.5
        ORDER BY score DESC
        LIMIT 10
      `, [cutoffTime]);

      const peakEmotions = peaksResult.rows.map((row: any) => ({
        emotion: row.emotion,
        peakTime: row.peak_time,
        intensity: parseFloat(row.intensity)
      }));

      // Simplified transitions - just return empty for now to avoid complex query
      const emotionTransitions: Array<{ from: string; to: string; frequency: number; avgTimeDiff: number }> = [];

      return {
        currentTrends,
        peakEmotions,
        emotionTransitions
      };
    } catch (error) {
      console.error('Error in getEmotionFlowAnalysis:', error);
      // Return empty data on error to prevent timeouts
      return {
        currentTrends: [],
        peakEmotions: [],
        emotionTransitions: []
      };
    }
  }

  /**
   * Get advanced user segmentation
   */
  async getUserSegmentation(): Promise<{
    segments: Array<{
      segmentId: string;
      name: string;
      userCount: number;
      characteristics: Record<string, any>;
      avgLifetimeValue: number;
      retentionRate: number;
    }>;
  }> {
    const result = await query(`
      WITH user_metrics AS (
        SELECT 
          u.id as user_id,
          COUNT(DISTINCT cs.id) as session_count,
          AVG(EXTRACT(EPOCH FROM (cs.ended_at - cs.started_at))/60) as avg_session_duration,
          COUNT(DISTINCT DATE(cs.started_at)) as active_days,
          MAX(cs.started_at) as last_active,
          AVG(
            CASE WHEN cm.emotions != '{}' THEN
              (cm.emotions->>'joy')::float + (cm.emotions->>'excitement')::float
            END
          ) as positive_emotion_avg,
          u.created_at
        FROM users u
        LEFT JOIN chat_sessions cs ON u.id = cs.user_id
        LEFT JOIN conversation_messages cm ON cs.id = cm.session_id
        GROUP BY u.id, u.created_at
      ),
      user_segments AS (
        SELECT 
          user_id,
          CASE 
            WHEN session_count >= 20 AND avg_session_duration >= 10 THEN 'power_users'
            WHEN session_count >= 5 AND positive_emotion_avg >= 0.6 THEN 'engaged_users'
            WHEN session_count <= 2 AND last_active < NOW() - INTERVAL '7 days' THEN 'at_risk_users'
            WHEN session_count >= 3 AND avg_session_duration < 5 THEN 'casual_users'
            ELSE 'new_users'
          END as segment,
          session_count,
          avg_session_duration,
          active_days,
          positive_emotion_avg
        FROM user_metrics
      )
      SELECT 
        segment,
        COUNT(*) as user_count,
        AVG(session_count) as avg_sessions,
        AVG(avg_session_duration) as avg_duration,
        AVG(active_days) as avg_active_days,
        AVG(positive_emotion_avg) as avg_positive_emotion
      FROM user_segments
      GROUP BY segment
      ORDER BY user_count DESC
    `);

    const segments = result.rows.map((row: any) => ({
      segmentId: row.segment,
      name: row.segment.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
      userCount: parseInt(row.user_count),
      characteristics: {
        avgSessions: parseFloat(row.avg_sessions) || 0,
        avgDuration: parseFloat(row.avg_duration) || 0,
        avgActiveDays: parseFloat(row.avg_active_days) || 0,
        avgPositiveEmotion: parseFloat(row.avg_positive_emotion) || 0
      },
      avgLifetimeValue: this.calculateLifetimeValue(row),
      retentionRate: this.calculateRetentionRate(row.segment)
    }));

    return { segments };
  }

  private calculateLifetimeValue(segmentData: any): number {
    // Simple LTV calculation based on engagement metrics
    const sessions = parseFloat(segmentData.avg_sessions) || 0;
    const duration = parseFloat(segmentData.avg_duration) || 0;
    const activeDays = parseFloat(segmentData.avg_active_days) || 0;
    
    return Math.round((sessions * duration * activeDays) / 100);
  }

  private calculateRetentionRate(segment: string): number {
    // Mock retention rates based on segment type
    const retentionRates: Record<string, number> = {
      power_users: 0.85,
      engaged_users: 0.70,
      casual_users: 0.45,
      new_users: 0.60,
      at_risk_users: 0.15
    };
    
    return retentionRates[segment] || 0.50;
  }
}

export const advancedAnalyticsService = new AdvancedAnalyticsService();
